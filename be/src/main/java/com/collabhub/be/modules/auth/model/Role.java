package com.collabhub.be.modules.auth.model;

import java.util.Set;
import java.util.EnumSet;

/**
 * User roles within the system.
 * Roles are scoped per account for multi-tenancy.
 * Each role has a set of permissions that define what actions the user can perform.
 */
public enum Role {
    /**
     * System administrator with full access to account resources.
     */
    ADMIN(EnumSet.of(
        // Account management
        Permission.ACCOUNT_READ,
        Permission.ACCOUNT_WRITE,
        Permission.ACCOUNT_DELETE,

        // User management
        Permission.USER_READ,
        Permission.USER_WRITE,
        Permission.USER_DELETE,
        Permission.USER_INVITE,

        // Collaboration hub management
        Permission.HUB_READ,
        Permission.HUB_WRITE,
        Permission.HUB_DELETE,
        Permission.HUB_INVITE,

        // Hub participant management
        Permission.HUB_PARTICIPANT_READ,
        Permission.HUB_PARTICIPANT_WRITE,
        Permission.HUB_PARTICIPANT_INVITE,
        Permission.HUB_PARTICIPANT_MANAGE,

        // Content management
        Permission.CONTENT_READ,
        Permission.CONTENT_WRITE,
        Permission.CONTENT_DELETE,
        Permission.CONTENT_REVIEW,

        // Post management
        Permission.POST_READ,
        Permission.POST_WRITE,
        Permission.POST_DELETE,
        Permission.POST_UPDATE,
        Permission.POST_COMMENT,

        // Invoice management
        Permission.INVOICE_READ,
        Permission.INVOICE_WRITE,
        Permission.INVOICE_DELETE,
        Permission.INVOICE_SEND,

        // Brand CRM
        Permission.BRAND_READ,
        Permission.BRAND_WRITE,
        Permission.BRAND_DELETE,

        // Company management
        Permission.COMPANY_READ,
        Permission.COMPANY_WRITE,
        Permission.COMPANY_DELETE,

        // Bank details management
        Permission.BANK_READ,
        Permission.BANK_WRITE,
        Permission.BANK_DELETE,

        // Chat permissions
        Permission.CHAT_READ,
        Permission.CHAT_WRITE,
        Permission.CHAT_CHANNEL_READ,
        Permission.CHAT_CHANNEL_MANAGE,

        // Brief permissions
        Permission.BRIEF_READ,
        Permission.BRIEF_WRITE,
        Permission.BRIEF_UPDATE,
        Permission.BRIEF_DELETE
    )),

    /**
     * Regular user with standard access permissions.
     */
    USER(EnumSet.of(
        // Basic account access
        Permission.ACCOUNT_READ,

        // Basic user access
        Permission.USER_READ,

        // Collaboration hub access
        Permission.HUB_READ,
        Permission.HUB_WRITE,
        Permission.HUB_INVITE,

        // Hub participant access
        Permission.HUB_PARTICIPANT_READ,
        Permission.HUB_PARTICIPANT_WRITE,
        Permission.HUB_PARTICIPANT_INVITE,

        // Content access
        Permission.CONTENT_READ,
        Permission.CONTENT_WRITE,
        Permission.CONTENT_REVIEW,

        // Post access
        Permission.POST_READ,
        Permission.POST_WRITE,
        Permission.POST_UPDATE,

        // Invoice access
        Permission.INVOICE_READ,
        Permission.INVOICE_WRITE,
        Permission.INVOICE_SEND,

        // Brand CRM access
        Permission.BRAND_READ,
        Permission.BRAND_WRITE,

        // Company management access
        Permission.COMPANY_READ,
        Permission.COMPANY_WRITE,

        // Bank details access
        Permission.BANK_READ,
        Permission.BANK_WRITE,

        // Chat permissions (no manage for regular users)
        Permission.CHAT_READ,
        Permission.CHAT_WRITE,
        Permission.CHAT_CHANNEL_READ,

        // Brief permissions
        Permission.BRIEF_READ,
        Permission.BRIEF_WRITE,
        Permission.BRIEF_UPDATE
        // Note: BRIEF_DELETE is admin-only for safety
    )),

    /**
     * External participant with hub-scoped access permissions.
     * Used for external users (brand contacts, freelance creators) who access
     * collaboration hubs via magic links. Access is restricted to assigned hubs only.
     */
    EXTERNAL_PARTICIPANT(EnumSet.of(
        // Hub access (scoped to assigned hubs via JWT claims)
        Permission.HUB_READ,

        // Post access (own posts only for editing, enforced at service layer)
        Permission.POST_READ,
        Permission.POST_WRITE,
        Permission.POST_UPDATE,
        Permission.POST_COMMENT,

        // Chat access
        Permission.CHAT_READ,
        Permission.CHAT_WRITE,
        Permission.CHAT_CHANNEL_READ,

        // Brief access (read-only)
        Permission.BRIEF_READ,
            Permission.HUB_PARTICIPANT_READ
    ));

    private final Set<Permission> permissions;

    Role(Set<Permission> permissions) {
        this.permissions = EnumSet.copyOf(permissions);
    }

    /**
     * Returns the permissions associated with this role.
     */
    public Set<Permission> getPermissions() {
        return EnumSet.copyOf(permissions);
    }

    /**
     * Checks if this role has the specified permission.
     */
    public boolean hasPermission(Permission permission) {
        return permissions.contains(permission);
    }

    /**
     * Returns the role name as a string for database storage.
     */
    public String getRoleName() {
        return this.name();
    }
    
    /**
     * Creates a Role from a string value.
     * 
     * @param roleName the role name string
     * @return the corresponding Role enum
     * @throws IllegalArgumentException if the role name is invalid
     */
    public static Role fromString(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        
        try {
            return Role.valueOf(roleName.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid role: " + roleName, e);
        }
    }
}
