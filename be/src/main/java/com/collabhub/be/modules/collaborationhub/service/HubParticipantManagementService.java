package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ConflictException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.modules.auth.service.ExternalUserMagicLinkService;
import com.collabhub.be.modules.collaborationhub.converter.HubParticipantConverter;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantListResponse;
import com.collabhub.be.modules.collaborationhub.dto.HubParticipantResponse;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing hub participant lifecycle and data operations.
 * Handles CRUD operations, participant removal, status management, and response conversion.
 */
@Service
public class HubParticipantManagementService {

    private static final Logger logger = LoggerFactory.getLogger(HubParticipantManagementService.class);

    // Error Messages
    private static final String PARTICIPANT_REMOVAL_FAILED_MESSAGE = "Failed to remove participant";
    private static final String CANNOT_RESEND_INTERNAL_MESSAGE = "Cannot resend invitation to internal participants";

    // Log Messages
    private static final String REMOVING_PARTICIPANT_LOG = "Removing participant {} from hub {} by user {}";
    private static final String PARTICIPANT_REMOVED_LOG = "PARTICIPANT_REMOVED: participantId={}, hubId={}, accountId={}, removedByUserId={}, participantRole={}, wasExternal={}";
    private static final String RESENDING_INVITATION_LOG = "Resending invitation to participant {} in hub {} by user {}";
    private static final String RETRIEVING_PARTICIPANTS_LOG = "Retrieving participants for hub {} with filters: role={}";
    private static final String RETRIEVING_PARTICIPANT_DETAILS_LOG = "Retrieving participant {} details for hub {}";

    private final HubParticipantRepositoryImpl participantRepository;
    private final HubParticipantConverter participantConverter;
    private final CollabHubPermissionService participantPermissionService;
    private final HubParticipantRoleService roleService;
    private final ExternalUserMagicLinkService magicLinkService;

    public HubParticipantManagementService(HubParticipantRepositoryImpl participantRepository,
                                         HubParticipantConverter participantConverter,
                                         CollabHubPermissionService participantPermissionService,
                                         HubParticipantRoleService roleService,
                                         ExternalUserMagicLinkService magicLinkService) {
        this.participantRepository = participantRepository;
        this.participantConverter = participantConverter;
        this.participantPermissionService = participantPermissionService;
        this.roleService = roleService;
        this.magicLinkService = magicLinkService;
    }

    /**
     * Gets a paginated list of hub participants with filtering and role-based visibility.
     *
     * @param hubId the hub ID
     * @param pageRequest pagination parameters
     * @param role optional role filter
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     * @return paginated list of participants
     */
    @Transactional(readOnly = true)
    public HubParticipantListResponse getHubParticipants(Long hubId, PageRequest pageRequest,
                                                       HubParticipantRole role) {
        logger.debug(RETRIEVING_PARTICIPANTS_LOG, hubId, role);

        participantPermissionService.validateCanParticipantAccessHubContent(hubId);

        List<HubParticipant> participants = participantRepository.findActiveParticipantsByHubId(hubId);
        long totalElements = participants.size();
        List<HubParticipantResponse> participantResponses = convertParticipantsToResponses(participants);
        HubParticipantListResponse.ParticipantFilters filters = createFilterInfo(role);

        return new HubParticipantListResponse(participantResponses, pageRequest, totalElements, filters);
    }

    /**
     * Gets details of a specific participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @return participant details
     */
    @Transactional(readOnly = true)
    public HubParticipantResponse getParticipantDetails(Long hubId, Long participantId) {
        logger.debug(RETRIEVING_PARTICIPANT_DETAILS_LOG, participantId, hubId);

        participantPermissionService.validateCanParticipantAccessHubContent(hubId);
        HubParticipant participant = participantRepository.findById(participantId);

        String displayName = getParticipantDisplayName(participant);
        return participantConverter.toResponse(participant, displayName);
    }

    /**
     * Removes a participant from a hub with proper validation.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void removeParticipant(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info(REMOVING_PARTICIPANT_LOG, participantId, hubId, userId);

        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);
        if (participant == null) {
            throw new ForbiddenException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND, "Participant not found");
        }

        roleService.validateLastAdminRemoval(participant, userId, hubId);

        performParticipantRemoval(participantId, hubId, participant);
        logParticipantRemoval(participantId, hubId, accountId, userId, participant);
    }

    /**
     * Resends invitation to an external participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param accountId the account ID for multi-tenancy
     * @param userId the requesting user ID
     */
    @Transactional
    public void resendInvitation(Long hubId, Long participantId, Long accountId, Long userId) {
        logger.info(RESENDING_INVITATION_LOG, participantId, hubId, userId);

        participantPermissionService.validateHubAdminAccess(hubId);
        HubParticipant participant = participantRepository.findById(participantId);
        validateExternalParticipant(participant);

        updateParticipantWithNewMagicLink(participant, accountId);
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Converts participants to response DTOs.
     */
    private List<HubParticipantResponse> convertParticipantsToResponses(List<HubParticipant> participants) {
        return participants.stream()
                .map(this::convertParticipantToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Converts a single participant to response DTO.
     */
    private HubParticipantResponse convertParticipantToResponse(HubParticipant participant) {
        String displayName = getParticipantDisplayName(participant);
        return participantConverter.toResponse(participant, displayName);
    }

    /**
     * Gets participant display name.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getName() != null && !participant.getName().trim().isEmpty()) {
            return participant.getName();
        }
        
        // Extract from email if name not available
        String email = participant.getEmail();
        if (email != null && email.contains("@")) {
            return email.substring(0, email.indexOf("@"));
        }
        
        return email;
    }

    /**
     * Creates filter information for the response.
     */
    private HubParticipantListResponse.ParticipantFilters createFilterInfo(HubParticipantRole currentRole) {
        // For now, create a simple filter - in production this would have proper filter options
        return new HubParticipantListResponse.ParticipantFilters(
                List.of("active", "pending", "removed"),
                List.of(HubParticipantRole.values()),
                "name",
                currentRole
        );
    }

    /**
     * Validates that a participant is external.
     */
    private void validateExternalParticipant(HubParticipant participant) {
        if (!participant.getIsExternal()) {
            throw new ForbiddenException(ErrorCode.INVALID_OPERATION, CANNOT_RESEND_INTERNAL_MESSAGE);
        }
    }

    /**
     * Updates participant with new magic link.
     */
    private void updateParticipantWithNewMagicLink(HubParticipant participant, Long accountId) {
        try {
            magicLinkService.createMagicLinkForEmail(participant.getEmail(), accountId);

            // For now, just log the update - in production this would update the database
            logger.info("Generated new magic link for participant {} in hub {}",
                       participant.getId(), participant.getHubId());
        } catch (Exception e) {
            logger.error("Failed to generate new magic link for participant {}: {}",
                        participant.getId(), e.getMessage());
            throw new ConflictException(ErrorCode.INVALID_OPERATION, "Failed to generate new invitation link");
        }
    }

    /**
     * Performs the actual participant removal.
     */
    private void performParticipantRemoval(Long participantId, Long hubId, HubParticipant participant) {
        // For now, use the DAO update method - in production this would be a proper repository method
        participant.setRemovedAt(LocalDateTime.now());
        participantRepository.update(participant);
    }

    /**
     * Logs participant removal for audit purposes.
     */
    private void logParticipantRemoval(Long participantId, Long hubId, Long accountId, Long userId, HubParticipant participant) {
        logger.info(PARTICIPANT_REMOVED_LOG, participantId, hubId, accountId, userId, 
                   participant.getRole(), participant.getIsExternal());
    }
}
