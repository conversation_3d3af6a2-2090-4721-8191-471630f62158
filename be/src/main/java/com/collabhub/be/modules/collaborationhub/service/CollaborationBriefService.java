package com.collabhub.be.modules.collaborationhub.service;

import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.converter.CollaborationBriefConverter;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationBriefRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import org.jooq.Record;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.CollaborationBrief;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing collaboration briefs within collaboration hubs.
 * Provides business logic for brief operations with simplified access control.
 *
 * Access Rules:
 * - Create: Any participant in the hub can create briefs
 * - Edit/Delete: Only the creator OR hub admin can edit/delete briefs
 * - View: Everyone in the hub can view all briefs
 */
@Service
public class CollaborationBriefService {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationBriefService.class);

    // Error Messages
    private static final String BRIEF_NOT_FOUND_MESSAGE = "Brief not found with ID: ";
    private static final String USER_NOT_PARTICIPANT_MESSAGE = "User is not a participant in this collaboration hub";
    private static final String BRIEF_ACCESS_DENIED_MESSAGE = "Only brief creators and hub admins can %s briefs";
    private static final String UNKNOWN_CREATOR_NAME = "Unknown";

    // Log Messages
    private static final String CREATING_BRIEF_LOG = "Creating brief '{}' in hub {} by user {}";
    private static final String CREATED_BRIEF_LOG = "Created brief {} in hub {}";
    private static final String UPDATING_BRIEF_LOG = "Updating brief {} in hub {} by user {}";
    private static final String UPDATED_BRIEF_LOG = "Updated brief {} in hub {}";
    private static final String RETRIEVING_BRIEF_LOG = "Retrieving brief {} in hub {} by user {}";
    private static final String RETRIEVING_BRIEFS_LOG = "Retrieving briefs for hub {} with filter '{}', page {}, size {} by user {}";
    private static final String RETRIEVED_BRIEFS_LOG = "Retrieved {} briefs out of {} total for hub {}";
    private static final String DELETING_BRIEF_LOG = "Deleting brief {} in hub {} by user {}";
    private static final String DELETED_BRIEF_LOG = "Deleted brief {} in hub {}";
    private static final String BRIEF_CREATION_ALLOWED_LOG = "Brief creation allowed for participant {} with role {}";

    // Database Field Names
    private static final String CREATOR_DISPLAY_NAME_FIELD = "creator_display_name";
    private static final String CREATOR_EMAIL_FIELD = "creator_email";

    private final CollaborationBriefRepositoryImpl briefRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final CollaborationBriefConverter briefConverter;
    private final CollabHubPermissionService collabHubPermissionService;
    private final JwtClaimsService jwtClaimsService;

    public CollaborationBriefService(CollaborationBriefRepositoryImpl briefRepository,
                                   HubParticipantRepositoryImpl participantRepository,
                                   CollaborationBriefConverter briefConverter,
                                   CollabHubPermissionService collabHubPermissionService,
                                   JwtClaimsService jwtClaimsService) {
        this.briefRepository = briefRepository;
        this.participantRepository = participantRepository;
        this.briefConverter = briefConverter;
        this.collabHubPermissionService = collabHubPermissionService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param request the brief creation request
     * @return the created brief response
     */
    @Transactional
    public CollaborationBriefResponse createBrief(@NotNull Long hubId, @Valid CollaborationBriefCreateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.info(CREATING_BRIEF_LOG, request.getTitle(), hubId, userContext.getEmail());

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId);

        CollaborationBrief brief = createBriefEntity(request, hubId, participant.getId());
        String creatorName = getParticipantDisplayName(participant);

        logger.info(CREATED_BRIEF_LOG, brief.getId(), hubId);
        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Updates an existing collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @param request the brief update request
     * @return the updated brief response
     */
    @Transactional
    public CollaborationBriefResponse updateBrief(@NotNull Long hubId, @NotNull Long briefId,
                                                 @Valid CollaborationBriefUpdateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.info(UPDATING_BRIEF_LOG, briefId, hubId, userContext.getEmail());

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId);
        CollaborationBrief brief = findBriefByIdAndHubId(briefId, hubId);
        validateBriefUpdatePermission(participant, brief);

        CollaborationBrief updatedBrief = updateBriefEntity(brief, request);
        String creatorName = getCreatorNameForBrief(brief.getCreatedByParticipantId());

        logger.info(UPDATED_BRIEF_LOG, briefId, hubId);
        return briefConverter.toResponse(updatedBrief, creatorName);
    }

    /**
     * Retrieves a specific brief by ID.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     * @return the brief response
     */
    @Transactional(readOnly = true)
    public CollaborationBriefResponse getBriefById(@NotNull Long hubId, @NotNull Long briefId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(RETRIEVING_BRIEF_LOG, briefId, hubId, userContext.getEmail());

        validateHubAccessAndGetParticipant(hubId);
        Record record = findBriefWithCreatorInfo(briefId, hubId);
        String creatorName = extractCreatorName(record);
        CollaborationBrief brief = record.into(CollaborationBrief.class);

        return briefConverter.toResponse(brief, creatorName);
    }

    /**
     * Retrieves a paginated list of briefs for a collaboration hub.
     *
     * @param hubId the collaboration hub ID
     * @param pageRequest the pagination request
     * @param titleFilter optional title filter
     * @return paginated list of brief list items
     */
    @Transactional(readOnly = true)
    public PageResponse<CollaborationBriefListItemDto> getBriefs(@NotNull Long hubId, @Valid PageRequest pageRequest,
                                                               String titleFilter) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(RETRIEVING_BRIEFS_LOG, hubId, titleFilter, pageRequest.getPage(), pageRequest.getSize(), userContext.getEmail());

        validateHubAccessAndGetParticipant(hubId);
        Long accountId = getAccountIdForHub(hubId);

        long totalCount = getBriefsCount(hubId, accountId, titleFilter);
        List<Record> records = getBriefsWithCreatorInfo(hubId, accountId, titleFilter, pageRequest.getOffset(), pageRequest.getSize());
        List<CollaborationBriefListItemDto> briefListItems = convertRecordsToBriefListItems(records);

        logger.debug(RETRIEVED_BRIEFS_LOG, briefListItems.size(), totalCount, hubId);
        return PageResponse.of(briefListItems, pageRequest, totalCount);
    }

    /**
     * Deletes a collaboration brief.
     *
     * @param hubId the collaboration hub ID
     * @param briefId the brief ID
     */
    @Transactional
    public void deleteBrief(@NotNull Long hubId, @NotNull Long briefId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.info(DELETING_BRIEF_LOG, briefId, hubId, userContext.getEmail());

        HubParticipant participant = validateHubAccessAndGetParticipant(hubId);
        CollaborationBrief brief = findBriefByIdAndHubId(briefId, hubId);
        validateBriefDeletePermission(participant, brief);

        briefRepository.deleteById(briefId);
        logger.info(DELETED_BRIEF_LOG, briefId, hubId);
    }

    // ========================================
    // Validation Helper Methods
    // ========================================

    /**
     * Validates hub access and returns the participant for the current user.
     */
    private HubParticipant validateHubAccessAndGetParticipant(@NotNull Long hubId) {
        collabHubPermissionService.validateCanParticipantAccessHubContent(hubId);

        UserContext userContext = jwtClaimsService.getCurrentUser();
        HubParticipant participant = participantRepository.findByHubIdAndEmail(hubId, userContext.getEmail());

        if (participant == null || participant.getRemovedAt() != null) {
            throw new ForbiddenException(ErrorCode.HUB_ACCESS_DENIED, USER_NOT_PARTICIPANT_MESSAGE);
        }
        return participant;
    }

    /**
     * Validates that a participant can update a specific brief.
     * Only the creator or hub admin can update briefs.
     */
    private void validateBriefUpdatePermission(@NotNull HubParticipant participant, @NotNull CollaborationBrief brief) {
        if (!canEditOrDeleteBrief(participant, brief.getCreatedByParticipantId())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, String.format(BRIEF_ACCESS_DENIED_MESSAGE, "update"));
        }
    }

    /**
     * Validates that a participant can delete a specific brief.
     * Only the creator or hub admin can delete briefs.
     */
    private void validateBriefDeletePermission(@NotNull HubParticipant participant, @NotNull CollaborationBrief brief) {
        if (!canEditOrDeleteBrief(participant, brief.getCreatedByParticipantId())) {
            throw new ForbiddenException(ErrorCode.BRIEF_ACCESS_DENIED, String.format(BRIEF_ACCESS_DENIED_MESSAGE, "delete"));
        }
    }


    // ========================================
    // Data Access Helper Methods
    // ========================================

    /**
     * Finds a brief by ID and hub ID with account validation.
     */
    private CollaborationBrief findBriefByIdAndHubId(@NotNull Long briefId, @NotNull Long hubId) {
        Long accountId = getAccountIdForHub(hubId);
        return briefRepository.findByIdAndHubIdAndAccountId(briefId, hubId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, BRIEF_NOT_FOUND_MESSAGE + briefId));
    }

    /**
     * Finds a brief with creator information.
     */
    private Record findBriefWithCreatorInfo(@NotNull Long briefId, @NotNull Long hubId) {
        return briefRepository.findBriefWithCreatorInfo(briefId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.BRIEF_NOT_FOUND, BRIEF_NOT_FOUND_MESSAGE + briefId));
    }

    /**
     * Gets total count of briefs with filter.
     */
    private long getBriefsCount(@NotNull Long hubId, @NotNull Long accountId, String titleFilter) {
        return briefRepository.countBriefsWithFilter(hubId, accountId, titleFilter);
    }

    /**
     * Gets briefs with creator information.
     */
    private List<Record> getBriefsWithCreatorInfo(@NotNull Long hubId, @NotNull Long accountId,
                                                String titleFilter, int offset, int size) {
        return briefRepository.findBriefsWithCreatorInfo(hubId, accountId, titleFilter, offset, size);
    }

    /**
     * Gets creator name for a brief by participant ID.
     */
    private String getCreatorNameForBrief(@NotNull Long createdByParticipantId) {
        HubParticipant creator = participantRepository.findById(createdByParticipantId);
        return creator != null ? getParticipantDisplayName(creator) : UNKNOWN_CREATOR_NAME;
    }

    /**
     * Gets account ID for a hub (for multi-tenancy validation).
     */
    private Long getAccountIdForHub(@NotNull Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        if (userContext.isInternalUser()) {
            return userContext.getAccountId();
        }
        // For external users, we need to get the account ID from the hub
        // This is a simplified approach - in production you might want to cache this
        return briefRepository.getAccountIdByHubId(hubId);
    }

    // ========================================
    // Business Logic Helper Methods
    // ========================================

    /**
     * Creates and persists a brief entity.
     */
    private CollaborationBrief createBriefEntity(@Valid CollaborationBriefCreateRequest request,
                                                @NotNull Long hubId, @NotNull Long participantId) {
        CollaborationBrief brief = briefConverter.toEntity(request, hubId, participantId);
        briefRepository.insert(brief);
        return brief;
    }

    /**
     * Updates and persists a brief entity.
     */
    private CollaborationBrief updateBriefEntity(@NotNull CollaborationBrief brief,
                                               @Valid CollaborationBriefUpdateRequest request) {
        CollaborationBrief updatedBrief = briefConverter.updateEntity(brief, request);
        briefRepository.update(updatedBrief);
        return updatedBrief;
    }

    /**
     * Converts records to brief list items (no access control filtering - everyone can see all briefs).
     */
    private List<CollaborationBriefListItemDto> convertRecordsToBriefListItems(@NotNull List<Record> records) {
        return records.stream()
                .map(record -> {
                    CollaborationBrief brief = record.into(CollaborationBrief.class);
                    String creatorName = extractCreatorName(record);
                    return briefConverter.toListItem(brief, creatorName);
                })
                .collect(Collectors.toList());
    }

    /**
     * Checks if a participant can edit or delete a brief.
     * Only the creator or hub admin can edit/delete briefs.
     */
    private boolean canEditOrDeleteBrief(@NotNull HubParticipant participant, @NotNull Long createdByParticipantId) {
        // Hub admin can edit/delete any brief
        if (participant.getRole() == HubParticipantRole.admin) {
            return true;
        }
        // Creator can edit/delete their own brief
        return participant.getId().equals(createdByParticipantId);
    }

    // ========================================
    // Utility Helper Methods
    // ========================================

    /**
     * Gets the display name for a participant.
     */
    private String getParticipantDisplayName(@NotNull HubParticipant participant) {
        return participant.getEmail(); // Could be enhanced to include user names
    }

    /**
     * Extracts creator name from a joined record.
     */
    private String extractCreatorName(@NotNull Record record) {
        String displayName = record.get(CREATOR_DISPLAY_NAME_FIELD, String.class);
        String creatorEmail = record.get(CREATOR_EMAIL_FIELD, String.class);

        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        } else {
            return creatorEmail != null ? creatorEmail : UNKNOWN_CREATOR_NAME;
        }
    }
}
