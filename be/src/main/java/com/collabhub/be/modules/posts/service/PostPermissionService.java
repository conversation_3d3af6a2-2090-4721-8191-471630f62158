package com.collabhub.be.modules.posts.service;

import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.ErrorCode;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.enums.HubParticipantRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for managing post permissions and access control.
 * Centralizes all permission-related logic for posts and reviews.
 * 
 * Handles permissions for:
 * - Creating posts
 * - Assigning reviewers to posts  
 * - Commenting on posts
 * 
 * Access Rules:
 * - Create Post: App ADMIN OR Hub roles (ADMIN, CONTENT_CREATOR, REVIEWER_CREATOR)
 * - Assign Reviewer: App ADMIN OR Hub roles (ADMIN, REVIEWER, REVIEWER_CREATOR)
 * - Comment on Post: Anyone who can view the post
 */
@Service
@Validated
public class PostPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(PostPermissionService.class);

    // Constants
    private static final String VALIDATING_REVIEWER_ASSIGNMENT_LOG = "Validating reviewer assignment permissions for post {} by user {}";
    private static final String REVIEWER_ASSIGNMENT_GRANTED_LOG = "Reviewer assignment permission granted for post {} by user {}";
    private static final String CANNOT_ASSIGN_REVIEWERS_MESSAGE = "Cannot assign reviewers to post";
    private static final String PARTICIPANT_CACHE_CLEARED_LOG = "Participant cache cleared for bulk permission calculation";

    private final PostRepositoryImpl postRepository;
    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final JwtClaimsService jwtClaimsService;

    // Request-scoped cache to avoid multiple DB calls for same participant
    private final Map<String, HubParticipant> participantCache = new HashMap<>();

    public PostPermissionService(PostRepositoryImpl postRepository,
                               PostReviewerRepositoryImpl postReviewerRepository,
                               HubParticipantRepositoryImpl participantRepository,
                               JwtClaimsService jwtClaimsService) {
        this.postRepository = postRepository;
        this.postReviewerRepository = postReviewerRepository;
        this.participantRepository = participantRepository;
        this.jwtClaimsService = jwtClaimsService;
    }

    // ========================================
    // Validation Methods (Throwing)
    // ========================================

    /**
     * Validates that the current user can view a specific post.
     * Optimized to use single query for post and hub validation.
     *
     * @param postId the post ID to validate access for
     * @throws ForbiddenException if user cannot view the post
     */
    @Transactional(readOnly = true)
    public void validateCanViewPost(@NotNull Long postId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();

        // Use optimized query that validates account ownership
        Post post = userContext.isExternalUser()
            ? postRepository.findById(postId)
            : postRepository.findByIdWithAccountValidation(postId, userContext.getAccountId())
                    .orElse(null);

        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }

        if (!canUserViewPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }
    }

    /**
     * Validates that the current user can edit a specific post.
     * Throws exception if edit access is denied.
     *
     * @param post the post to validate edit access for
     * @throws ForbiddenException if user cannot edit the post
     */
    @Transactional(readOnly = true)
    public void validateCanEditPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        if (!canUserEditPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_EDIT_POST_MESSAGE);
        }
    }

    /**
     * Validates that the current user can edit a specific post by ID.
     * Throws exception if edit access is denied.
     *
     * @param postId the post ID to validate edit access for
     * @throws ForbiddenException if user cannot edit the post
     */
    @Transactional(readOnly = true)
    public void validateCanEditPost(@NotNull Long postId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }
        validateCanEditPost(post);
    }

    /**
     * Validates that the current user can review a specific post.
     * Throws exception if review access is denied.
     *
     * @param post the post to validate review access for
     * @throws ForbiddenException if user cannot review the post
     */
    @Transactional(readOnly = true)
    public void validateCanReviewPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        if (!canUserReviewPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_REVIEW_POST_MESSAGE);
        }
    }

    /**
     * Validates that the current user can review a specific post by ID.
     * Throws exception if review access is denied.
     *
     * @param postId the post ID to validate review access for
     * @throws ForbiddenException if user cannot review the post
     */
    @Transactional(readOnly = true)
    public void validateCanReviewPost(@NotNull Long postId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }
        validateCanReviewPost(post);
    }

    /**
     * Validates that a user can create posts in a hub.
     * Throws exception if creation access is denied.
     *
     * @param role the hub participant role to validate
     * @throws ForbiddenException if role cannot create posts
     */
    @Transactional(readOnly = true)
    public void validateCanCreatePost(@NotNull HubParticipantRole role) {
        if (!canRoleCreatePosts(role)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.CANNOT_CREATE_POSTS_MESSAGE + ": " + role);
        }
    }

    /**
     * Validates that the current user can assign reviewers to a post.
     * Throws exception if reviewer assignment access is denied.
     *
     * Rules:
     * - App-level role is ADMIN, or
     * - Hub role is one of: ADMIN, REVIEWER, REVIEWER_CREATOR
     * User must be a participant if not App ADMIN.
     *
     * @param postId the post ID to assign reviewers to
     * @throws ForbiddenException if user cannot assign reviewers
     */
    @Transactional(readOnly = true)
    public void validateCanAssignReviewer(@NotNull Long postId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug(VALIDATING_REVIEWER_ASSIGNMENT_LOG, postId, userContext.getEmail());

        Post post = postRepository.findById(postId);
        if (post == null) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                PostConstants.ACCESS_DENIED_MESSAGE + ": " + postId);
        }

        if (!canUserAssignReviewers(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS,
                CANNOT_ASSIGN_REVIEWERS_MESSAGE + ": " + postId);
        }

        logger.debug(REVIEWER_ASSIGNMENT_GRANTED_LOG, postId, userContext.getEmail());
    }

    // ========================================
    // Permission Check Methods (Non-throwing)
    // ========================================

    /**
     * Checks if a user can edit a specific post.
     * Post creator (CONTENT_CREATOR who created the post) and ADMIN can edit.
     *
     * @param post the post to check edit permissions for
     * @return true if user can edit the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserEditPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            if (participant == null) {
                return false;
            }

            return isPostCreator(post, participant) || isHubAdmin(participant);
        } catch (Exception e) {
            logger.warn("Error checking edit permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a user can review a specific post.
     * Only assigned reviewers and admins can review.
     *
     * @param post the post to check review permissions for
     * @return true if user can review the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserReviewPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            if (participant == null || participant.getRemovedAt() != null) {
                return false;
            }

            return postReviewerRepository.isParticipantAssignedAsReviewer(post.getId(), participant.getId());
        } catch (Exception e) {
            logger.warn("Error checking review permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a user can comment on a specific post.
     * Anyone who can view a post can also comment on it.
     *
     * @param post the post to check comment permissions for
     * @return true if user can comment on the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserCommentOnPost(@Valid @NotNull Post post) {
        return canUserViewPost(post);
    }

    /**
     * Checks if a user can view a specific post.
     * All hub participants can see ALL posts.
     *
     * @param post the post to check view permissions for
     * @return true if user can view the post, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserViewPost(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        try {
            HubParticipant participant = getParticipantForPost(post, userContext);
            return participant != null && participant.getRemovedAt() == null;
        } catch (Exception e) {
            logger.warn("Error checking view permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a user can assign reviewers to a post.
     * App ADMIN OR Hub roles (ADMIN, REVIEWER, REVIEWER_CREATOR) can assign reviewers.
     *
     * @param post the post to check reviewer assignment permissions for
     * @return true if user can assign reviewers, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canUserAssignReviewers(@Valid @NotNull Post post) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        try {
            // 1. App-level ADMIN has full access
            if (userContext.getRole() == Role.ADMIN) {
                return true;
            }

            // 2. Hub participant with reviewer assignment role
            HubParticipant participant = getParticipantForPost(post, userContext);
            if (participant == null || participant.getRemovedAt() != null) {
                return false;
            }

            return hasReviewerAssignmentRole(participant);
        } catch (Exception e) {
            logger.warn("Error checking reviewer assignment permissions for post {} and user {}: {}",
                       post.getId(), userContext.getEmail(), e.getMessage());
            return false;
        }
    }

    /**
     * Checks if a role can create posts.
     *
     * @param role the hub participant role to check
     * @return true if role can create posts, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean canRoleCreatePosts(@NotNull HubParticipantRole role) {
        return role == HubParticipantRole.admin ||
               role == HubParticipantRole.content_creator ||
               role == HubParticipantRole.reviewer_creator;
    }

    /**
     * Bulk calculates permissions for multiple posts to avoid N+1 queries.
     * Uses participant caching to minimize database calls.
     *
     * @param posts the list of posts to calculate permissions for
     * @return map of post ID to permissions
     */
    @Transactional(readOnly = true)
    public Map<Long, PostListPermissions> calculateBulkPostPermissions(@Valid @NotNull List<Post> posts) {
        Map<Long, PostListPermissions> permissionsMap = new HashMap<>();

        // Clear cache at start of bulk operation
        participantCache.clear();

        for (Post post : posts) {
            boolean canEdit = canUserEditPost(post);
            boolean canReview = canUserReviewPost(post);
            boolean canComment = canUserCommentOnPost(post);

            permissionsMap.put(post.getId(), new PostListPermissions(canEdit, canReview, canComment));
        }

        // Clear cache after bulk operation
        participantCache.clear();

        return permissionsMap;
    }

    /**
     * Record to hold post list permissions.
     */
    public record PostListPermissions(boolean canEdit, boolean canReview, boolean canComment) {}

    /**
     * Validates if a participant can be assigned as a reviewer.
     *
     * @param participantId the participant ID to validate
     * @param hubId the hub ID to validate against
     * @return true if participant can be a reviewer, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean isValidReviewer(@NotNull Long participantId, @NotNull Long hubId) {
        HubParticipant reviewer = participantRepository.findById(participantId);

        if (!isValidParticipant(reviewer, hubId)) {
            logger.warn("Invalid reviewer ID {} for hub {}", participantId, hubId);
            return false;
        }

        if (!hasReviewerRole(reviewer)) {
            logger.warn("Reviewer {} does not have review permissions", participantId);
            return false;
        }

        return true;
    }

    // ========================================
    // Private Helper Methods
    // ========================================

    /**
     * Gets participant for a post with caching to avoid duplicate queries.
     * Uses hub ID and email as cache key.
     */
    private HubParticipant getParticipantForPost(Post post, UserContext userContext) {
        String cacheKey = post.getHubId() + ":" + userContext.getEmail();
        return participantCache.computeIfAbsent(cacheKey,
            key -> participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail()));
    }

    /**
     * Checks if participant is the post creator.
     */
    private boolean isPostCreator(Post post, HubParticipant participant) {
        return post.getCreatorParticipantId().equals(participant.getId());
    }

    /**
     * Checks if participant is a hub admin.
     */
    private boolean isHubAdmin(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin;
    }

    /**
     * Validates if participant is valid for the hub.
     */
    private boolean isValidParticipant(HubParticipant participant, Long hubId) {
        return participant != null &&
               participant.getHubId().equals(hubId) &&
               participant.getRemovedAt() == null;
    }

    /**
     * Checks if participant has reviewer role.
     */
    private boolean hasReviewerRole(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin ||
               participant.getRole() == HubParticipantRole.reviewer ||
               participant.getRole() == HubParticipantRole.reviewer_creator;
    }

    /**
     * Checks if participant has reviewer assignment role.
     * Can assign reviewers: ADMIN, REVIEWER, REVIEWER_CREATOR
     */
    private boolean hasReviewerAssignmentRole(HubParticipant participant) {
        return participant.getRole() == HubParticipantRole.admin ||
               participant.getRole() == HubParticipantRole.reviewer ||
               participant.getRole() == HubParticipantRole.reviewer_creator;
    }
}
