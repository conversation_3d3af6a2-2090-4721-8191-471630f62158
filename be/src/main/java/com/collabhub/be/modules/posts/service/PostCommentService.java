package com.collabhub.be.modules.posts.service;

import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostCommentConverter;
import com.collabhub.be.modules.posts.dto.PostCommentCreateRequest;
import com.collabhub.be.modules.posts.util.PostParticipantUtil;
import com.collabhub.be.modules.chat.service.MentionService;
import com.collabhub.be.modules.chat.dto.MentionDto;
import com.collabhub.be.modules.posts.dto.PostCommentUpdateRequest;
import com.collabhub.be.modules.posts.dto.PostCommentResponse;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.posts.dto.PostCommentListResponse;
import com.collabhub.be.modules.posts.repository.PostCommentRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.exception.ForbiddenException;
import org.jooq.generated.tables.pojos.PostComment;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing post comments.
 * Handles CRUD operations for comments with permission-based access control.
 */
@Service
public class PostCommentService {

    private static final Logger logger = LoggerFactory.getLogger(PostCommentService.class);

    private final PostCommentRepositoryImpl commentRepository;
    private final PostRepositoryImpl postRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final PostCommentConverter commentConverter;
    private final PostPermissionService postPermissionService;
    private final CollabHubPermissionService collabHubPermissionService;
    private final PostParticipantUtil participantUtil;
    private final MentionService mentionService;

    public PostCommentService(PostCommentRepositoryImpl commentRepository,
                             PostRepositoryImpl postRepository,
                             HubParticipantRepositoryImpl participantRepository,
                             PostCommentConverter commentConverter,
                             PostPermissionService postPermissionService,
                             CollabHubPermissionService collabHubPermissionService,
                             PostParticipantUtil participantUtil,
                             MentionService mentionService) {
        this.commentRepository = commentRepository;
        this.postRepository = postRepository;
        this.participantRepository = participantRepository;
        this.commentConverter = commentConverter;
        this.postPermissionService = postPermissionService;
        this.collabHubPermissionService = collabHubPermissionService;
        this.participantUtil = participantUtil;
        this.mentionService = mentionService;
    }

    /**
     * Creates a new comment on a post.
     */
    @Transactional
    public PostCommentResponse createComment(Long postId, PostCommentCreateRequest request,
                                           UserContext userContext) {
        logger.debug("Creating comment on post {} by user {}", postId, userContext.getUserId());

        Post post = findByPostIdAndAccountIdOrThrow(postId, userContext.getAccountId());
        validateCanCommentOnPost(post);
        HubParticipant participant = getParticipantForUser(post.getHubId(), userContext);

        PostComment comment = commentConverter.toPostComment(request, postId, participant.getId());
        commentRepository.insert(comment);

        // Process mentions in the comment content
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), post.getHubId());
        logger.debug("Processed {} mentions in comment {}", mentions.size(), comment.getId());

        logger.debug("Successfully created comment {} on post {}", comment.getId(), postId);
        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Updates an existing comment.
     */
    @Transactional
    public PostCommentResponse updateComment(Long commentId, PostCommentUpdateRequest request,
                                           UserContext userContext) {
        logger.debug("Updating comment {} by user {}", commentId, userContext.getUserId());

        PostComment comment = findByIdAndAccountIdOrThrow(commentId, userContext.getAccountId());
        Post post = findByPostIdAndAccountIdOrThrow(comment.getPostId(), userContext.getAccountId());
        validateCanEditComment(comment, post, userContext);

        boolean updated = commentRepository.updateCommentContent(commentId, request.getContent());
        if (!updated) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE);
        }

        // Fetch updated comment
        comment = commentRepository.findByIdWithAccountValidation(commentId, userContext.getAccountId())
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE));

        HubParticipant participant = participantRepository.findById(comment.getParticipantId());

        // Process mentions in the updated comment content
        List<MentionDto> mentions = mentionService.parseMentions(request.getContent(), post.getHubId());
        logger.debug("Processed {} mentions in updated comment {}", mentions.size(), commentId);

        logger.debug("Successfully updated comment {}", commentId);
        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Deletes a comment.
     */
    @Transactional
    public void deleteComment(Long commentId, UserContext userContext) {
        logger.debug("Deleting comment {} by user {}", commentId, userContext.getUserId());

        PostComment comment = findByIdAndAccountIdOrThrow(commentId, userContext.getAccountId());
        Post post = findByPostIdAndAccountIdOrThrow(comment.getPostId(), userContext.getAccountId());
        validateCanDeleteComment(comment, post, userContext);

        boolean deleted = commentRepository.deleteComment(commentId);
        if (!deleted) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE);
        }

        logger.debug("Successfully deleted comment {}", commentId);
    }

    /**
     * Retrieves a specific comment by ID.
     */
    @Transactional(readOnly = true)
    public PostCommentResponse getComment(Long commentId, UserContext userContext) {
        logger.debug("Retrieving comment {} for user {}", commentId, userContext.getUserId());

        PostComment comment = findByIdAndAccountIdOrThrow(commentId, userContext.getAccountId());
        Post post = findByPostIdAndAccountIdOrThrow(comment.getPostId(), userContext.getAccountId());
        postPermissionService.validateCanViewPost(post.getId());

        HubParticipant participant = participantRepository.findById(comment.getParticipantId());

        // Process mentions in the comment content
        List<MentionDto> mentions = mentionService.parseMentions(comment.getContent(), post.getHubId());

        return buildCommentResponseWithMentions(comment, participant, userContext, mentions);
    }

    /**
     * Retrieves comments for a post with pagination.
     */
    @Transactional(readOnly = true)
    public PostCommentListResponse getCommentsForPost(Long postId, UserContext userContext,
                                                     int page, int size) {
        logger.debug("Retrieving comments for post {} (page {}, size {}) for user {}", postId, page, size, userContext.getUserId());

        Post post = findByPostIdAndAccountIdOrThrow(postId, userContext.getAccountId());
        postPermissionService.validateCanViewPost(postId);

        int offset = page * size;
        List<PostComment> comments = commentRepository.findByPostIdWithPagination(postId, userContext.getAccountId(), offset, size);
        long totalCount = commentRepository.countByPostId(postId, userContext.getAccountId());

        // Bulk load participant information to avoid N+1 queries
        Map<Long, HubParticipant> participantMap = loadParticipantsForComments(comments);

        List<PostCommentListResponse.PostCommentItem> commentItems = comments.stream()
                .map(comment -> {
                    HubParticipant participant = participantMap.get(comment.getParticipantId());
                    PostCommentListResponse.CommentAuthor author = participantUtil.createListCommentAuthor(participant);

                    boolean canEdit = canUserEditComment(comment, participant, userContext);
                    boolean canDelete = canUserDeleteComment(comment, participant, userContext);

                    // Process mentions for each comment
                    List<MentionDto> mentions = mentionService.parseMentions(comment.getContent(), post.getHubId());

                    return commentConverter.toPostCommentListItemWithMentions(comment, author, canEdit, canDelete, mentions);
                })
                .collect(Collectors.toList());

        return commentConverter.createCommentListResponse(commentItems, totalCount, size, page);
    }

    // Private helper methods

    private Post findByPostIdAndAccountIdOrThrow(Long postId, Long accountId) {
        return postRepository.findByIdWithAccountValidation(postId, accountId)
                .orElseThrow(() -> {
                    logger.warn("Post not found: ID={}, accountId={}", postId, accountId);
                    return new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE);
                });
    }

    private PostComment findByIdAndAccountIdOrThrow(Long commentId, Long accountId) {
        return commentRepository.findByIdWithAccountValidation(commentId, accountId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.COMMENT_NOT_FOUND_MESSAGE));
    }

    private void validateCanCommentOnPost(Post post) {
        // Validate hub access first
        collabHubPermissionService.validateCanParticipantAccessHubContent(post.getHubId());
        // Then validate post-specific comment permissions
        if (!postPermissionService.canUserCommentOnPost(post)) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot comment on this post");
        }
    }

    private void validateCanEditComment(PostComment comment, Post post, UserContext userContext) {
        // For now, only comment author can edit (implement proper logic later)
        HubParticipant participant = participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
        if (participant == null || !comment.getParticipantId().equals(participant.getId())) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot edit this comment");
        }
    }

    private void validateCanDeleteComment(PostComment comment, Post post, UserContext userContext) {
        // For now, only comment author can delete (implement proper logic later)
        HubParticipant participant = participantRepository.findByHubIdAndEmail(post.getHubId(), userContext.getEmail());
        if (participant == null || !comment.getParticipantId().equals(participant.getId())) {
            throw new ForbiddenException(ErrorCode.INSUFFICIENT_PERMISSIONS, "Cannot delete this comment");
        }
    }

    private HubParticipant getParticipantForUser(Long hubId, UserContext userContext) {
        return participantRepository.findByHubIdAndEmail(hubId, userContext.getEmail());
    }

    /**
     * Checks if user can edit a comment (only comment author can edit).
     */
    private boolean canUserEditComment(PostComment comment, HubParticipant participant, UserContext userContext) {
        return participant != null && comment.getParticipantId().equals(participant.getId());
    }

    /**
     * Checks if user can delete a comment (only comment author can delete).
     */
    private boolean canUserDeleteComment(PostComment comment, HubParticipant participant, UserContext userContext) {
        return participant != null && comment.getParticipantId().equals(participant.getId());
    }

    private PostCommentResponse buildCommentResponse(PostComment comment, HubParticipant participant, UserContext userContext) {
        PostCommentResponse.CommentAuthor author = participantUtil.createCommentAuthor(participant);

        boolean canEdit = canUserEditComment(comment, participant, userContext);
        boolean canDelete = canUserDeleteComment(comment, participant, userContext);
        PostCommentResponse.CommentPermissions permissions = commentConverter.createCommentPermissions(canEdit, canDelete);

        return commentConverter.toPostCommentResponse(comment, author, permissions);
    }

    private PostCommentResponse buildCommentResponseWithMentions(PostComment comment, HubParticipant participant,
                                                               UserContext userContext, List<MentionDto> mentions) {
        PostCommentResponse.CommentAuthor author = participantUtil.createCommentAuthor(participant);

        boolean canEdit = canUserEditComment(comment, participant, userContext);
        boolean canDelete = canUserDeleteComment(comment, participant, userContext);
        PostCommentResponse.CommentPermissions permissions = commentConverter.createCommentPermissions(canEdit, canDelete);

        return commentConverter.toPostCommentResponseWithMentions(comment, author, permissions, mentions);
    }

    private Map<Long, HubParticipant> loadParticipantsForComments(List<PostComment> comments) {
        List<Long> participantIds = comments.stream()
                .map(PostComment::getParticipantId)
                .distinct()
                .collect(Collectors.toList());

        return participantUtil.bulkLoadHubParticipants(participantIds);
    }
}
